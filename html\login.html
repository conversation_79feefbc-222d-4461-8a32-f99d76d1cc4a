<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录 - 中国REITs论坛</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            margin: 0px;
            background-image: none;
            position: relative;
            left: -0px;
            width: 1912px;
            margin-left: auto;
            margin-right: auto;
            text-align: left;
            font-family: "STHeiti SC Medium", "STHeiti SC", sans-serif;
            background-color: rgba(242, 242, 242, 1);
            min-height: 954px;
            overflow-x: auto;
        }

        /* 背景层 */
        .background-layer {
            position: absolute;
            left: 0px;
            top: 0px;
            width: 1912px;
            height: 954px;
            background-color: rgba(242, 242, 242, 1);
            z-index: 0;
        }

        /* 背景图片层 */
        .background-image {
            position: absolute;
            left: 0px;
            top: 0px;
            width: 1912px;
            height: 954px;
            background-image: url('../mastergo/images/登录-密码登录/u1.png');
            background-size: 1912px 954px;
            background-position: 0 0;
            background-repeat: no-repeat;
            z-index: 1;
        }

        /* 半透明遮罩层 */
        .overlay-layer {
            position: absolute;
            left: 0px;
            top: 0px;
            width: 1912px;
            height: 954px;
            background-color: rgba(255, 255, 255, 0.4980392156862745);
            z-index: 2;
        }

        /* 登录容器 - 按照原型位置定位 */
        .login-container {
            position: absolute;
            left: 1172px;
            top: 201px;
            width: 560px;
            height: 551px;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 20px;
            padding: 30px 60px 60px 60px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            z-index: 3;
        }

        /* Logo区域 */
        .logo-section {
            position: absolute;
            left: 40px;
            top: 30px;
            width: 195px;
            height: 40px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .logo-icon {
            width: 40px;
            height: 40px;
            background-color: #1868F1;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
        }

        .logo-text {
            font-family: "STHeiti SC Medium", "STHeiti SC", sans-serif;
            font-weight: 400;
            font-size: 24px;
            color: #1868F1;
        }

        .login-title {
            font-size: 20px;
            font-weight: 400;
            color: #1868F1;
            text-align: center;
            margin-bottom: 30px;
            margin-top: 20px;
            font-family: "STHeiti SC Medium", "STHeiti SC", sans-serif;
        }

        .login-tabs {
            display: flex;
            margin-bottom: 30px;
            position: relative;
        }

        .tab-button {
            background: none;
            border: none;
            font-size: 20px;
            color: #666;
            cursor: pointer;
            position: relative;
            transition: color 0.3s;
            font-family: "STHeiti SC Medium", "STHeiti SC", sans-serif;
            font-weight: 400;
            padding: 0;
            margin-right: 40px;
        }

        .tab-button.active {
            color: #1868f1;
        }

        .tab-button.active::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 0;
            width: 50px;
            height: 5px;
            background-color: #1868f1;
            border-radius: 2px;
        }

        .tab-button:not(.active) {
            color: #333;
        }

        .form-group {
            margin-bottom: 20px;
            position: relative;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-size: 16px;
            font-family: "STHeiti SC Medium", "STHeiti SC", sans-serif;
            font-weight: 400;
            text-align: right;
            width: 64px;
            position: absolute;
            left: -74px;
            top: 17px;
        }

        .form-input {
            width: 426px;
            height: 50px;
            padding: 2px 10px;
            border: 1px solid #d7d7d7;
            border-radius: 5px;
            font-size: 16px;
            font-family: "STHeiti SC Medium", "STHeiti SC", sans-serif;
            background-color: #ffffff;
            color: #333;
            transition: border-color 0.3s;
            box-sizing: border-box;
        }

        .form-input::placeholder {
            color: #AAAAAA;
        }

        .form-input:focus {
            outline: none;
            border-color: #1868f1;
        }

        .verification-group {
            display: flex;
            gap: 12px;
        }

        .verification-input {
            flex: 1;
        }

        .verification-btn {
            padding: 12px 20px;
            background-color: #1868f1;
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            white-space: nowrap;
            transition: background-color 0.3s;
        }

        .verification-btn:hover {
            background-color: #1557d6;
        }

        .verification-btn:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }

        .remember-forgot {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            position: relative;
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 12px;
            font-family: "STHeiti SC Medium", "STHeiti SC", sans-serif;
        }

        .checkbox {
            width: 12px;
            height: 12px;
            border: 1px solid #7f7f7f;
            border-radius: 2px;
            cursor: pointer;
            position: relative;
            background-color: white;
        }

        .checkbox.checked {
            background-color: #1868f1;
            border-color: #1868f1;
        }

        .checkbox.checked::after {
            content: '✓';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 10px;
        }

        .forgot-link {
            color: #333;
            text-decoration: none;
            font-size: 16px;
            font-family: "STHeiti SC Medium", "STHeiti SC", sans-serif;
            position: absolute;
            right: 218px;
            top: 92px;
        }

        .forgot-link:hover {
            text-decoration: underline;
        }

        .login-btn {
            width: 500px;
            height: 50px;
            background-color: #1868f1;
            color: white;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            font-family: "STHeiti SC Medium", "STHeiti SC", sans-serif;
            font-weight: 400;
            cursor: pointer;
            transition: background-color 0.3s;
            margin-bottom: 20px;
            margin-top: 10px;
        }

        .login-btn:hover {
            background-color: #1557d6;
        }

        .divider {
            text-align: center;
            margin: 30px 0;
            position: relative;
            color: #AAAAAA;
            font-size: 16px;
            font-family: "STHeiti SC Medium", "STHeiti SC", sans-serif;
        }

        .divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background-color: #d7d7d7;
        }

        .divider span {
            background-color: rgba(255, 255, 255, 0.8);
            padding: 0 10px;
        }

        .other-login {
            text-align: center;
        }

        .forum-login-btn {
            color: #7F7F7F;
            text-decoration: none;
            font-size: 16px;
            font-family: "STHeiti SC Medium", "STHeiti SC", sans-serif;
            font-weight: 400;
            display: inline-block;
            transition: all 0.3s;
        }

        .forum-login-btn:hover {
            color: #1868f1;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .countdown {
            color: #666;
        }

        /* 注册链接样式 */
        .register-link {
            position: absolute;
            left: 1547px;
            top: 243px;
            width: 155px;
            height: 16px;
            font-family: "STHeiti SC Medium", "STHeiti SC", sans-serif;
            font-size: 16px;
            text-align: right;
            color: #333;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .register-link .register-text {
            color: #1868F1;
        }

        .register-arrow {
            width: 6px;
            height: 10px;
        }

        /* 标注样式 */
        .annotation {
            position: absolute;
            width: 20px;
            height: 20px;
            background-color: white;
            border: 2px solid #ff0000;
            border-radius: 3px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
            color: #ff0000;
            z-index: 1000;
        }

        .annotation-1 {
            top: 15px;
            left: 15px;
        }

        .annotation-2 {
            top: 15px;
            right: 15px;
        }

        .annotation-3 {
            top: 90px;
            right: 15px;
        }

        /* 响应式设计 */
        @media (max-width: 1440px) {
            body {
                width: 100vw;
            }

            .background-layer,
            .overlay-layer,
            .background-image {
                width: 100vw;
                height: 100vh;
            }

            .login-container {
                left: 50%;
                top: 50%;
                transform: translate(-50%, -50%);
            }

            .logo-section {
                left: 40px;
                top: 30px;
            }

            .register-link {
                right: 40px;
                left: auto;
                top: 30px;
            }
        }
    </style>
</head>
<body>
    <!-- 背景层 -->
    <div class="background-layer"></div>

    <!-- 背景图片层 -->
    <div class="background-image"></div>

    <!-- 半透明遮罩层 -->
    <div class="overlay-layer"></div>

    <!-- Logo区域 -->
    <div class="logo-section">
        <div class="logo-icon">logo</div>
        <div class="logo-text">rev-REITs平台</div>
    </div>

    <!-- 注册链接 -->
    <a href="#" class="register-link">
        <span>还没有账号，去</span>
        <span class="register-text">注册</span>
        <svg class="register-arrow" viewBox="0 0 6 10">
            <path d="M1 1L5 5L1 9" stroke="#1868F1" stroke-width="1" fill="none"/>
        </svg>
    </a>

    <!-- 标注 -->
    <div class="annotation annotation-1">1</div>
    <div class="annotation annotation-2">2</div>
    <div class="annotation annotation-3">3</div>

    <!-- 登录容器 -->
    <div class="login-container">
        <h1 class="login-title">密码登录</h1>

        <div class="login-tabs">
            <button class="tab-button active" data-tab="password">密码登录</button>
            <button class="tab-button" data-tab="sms">短信登录</button>
        </div>

        <!-- 密码登录 -->
        <div id="password-tab" class="tab-content active">
            <form id="password-form">
                <div class="form-group" style="margin-top: 40px;">
                    <label class="form-label">用&nbsp;户&nbsp;名</label>
                    <input type="text" class="form-input" id="username" placeholder="请输入用户名/手机号" required>
                </div>

                <div class="form-group" style="margin-top: 30px;">
                    <label class="form-label">密&nbsp;&nbsp;&nbsp;&nbsp;码</label>
                    <input type="password" class="form-input" id="password" placeholder="请输入登录密码" required>
                    <img src="../mastergo/images/登录-密码登录/u19.png" style="position: absolute; right: 10px; top: 15px; width: 20px; height: 20px; cursor: pointer;" onclick="togglePassword()" alt="显示/隐藏密码">
                </div>

                <div class="remember-forgot">
                    <div class="checkbox-group">
                        <div class="checkbox" id="remember-checkbox"></div>
                        <label for="remember-checkbox">阅读并同意<span style="color:#1868F1;">用户协议</span>、<span style="color:#1868F1;">隐私声明</span>、<span style="color:#1868F1;">产品使用条款</span></label>
                    </div>
                    <a href="#" class="forgot-link">忘记密码</a>
                </div>

                <button type="submit" class="login-btn">登录</button>
            </form>
        </div>

        <!-- 短信登录 -->
        <div id="sms-tab" class="tab-content">
            <form id="sms-form">
                <div class="form-group">
                    <label class="form-label">手机号</label>
                    <input type="tel" class="form-input" id="phone" placeholder="请输入手机号" required>
                </div>
                
                <div class="form-group">
                    <label class="form-label">验证码</label>
                    <div class="verification-group">
                        <input type="text" class="form-input verification-input" id="verification-code" placeholder="请输入验证码" required>
                        <button type="button" class="verification-btn" id="send-code-btn">获取验证码</button>
                    </div>
                </div>

                <button type="submit" class="login-btn">登录</button>
            </form>
        </div>

        <div class="divider">
            <span>其他登录方式</span>
        </div>

        <div class="other-login">
            <a href="#" class="forum-login-btn">中国REITs论坛账号登录</a>
        </div>

        <!-- 开发备注 -->
        <div style="position: absolute; left: -281px; top: 452px; width: 241px; height: 99px; background-color: rgba(255, 223, 37, 1); padding: 10px; box-shadow: 5px 5px 5px rgba(0, 0, 0, 0.2); font-family: 'STHeiti SC Medium', 'STHeiti SC', sans-serif; font-size: 14px; line-height: 20px; display: none;" id="dev-notes">
            <p>1、确认是否必须登录才能进入首页</p>
            <p>2、点击"中国REITs论坛账号登录"跳转对应网站验证，需对接</p>
        </div>
    </div>

    <script>
        // 显示/隐藏密码
        function togglePassword() {
            const passwordInput = document.getElementById('password');
            const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
            passwordInput.setAttribute('type', type);
        }

        // 标签页切换
        document.querySelectorAll('.tab-button').forEach(button => {
            button.addEventListener('click', () => {
                const tabName = button.dataset.tab;

                // 移除所有活动状态
                document.querySelectorAll('.tab-button').forEach(btn => btn.classList.remove('active'));
                document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));

                // 添加当前活动状态
                button.classList.add('active');
                document.getElementById(`${tabName}-tab`).classList.add('active');

                // 更新标题
                const title = document.querySelector('.login-title');
                if (tabName === 'password') {
                    title.textContent = '密码登录';
                } else {
                    title.textContent = '短信登录';
                }
            });
        });

        // 记住我复选框
        const rememberCheckbox = document.getElementById('remember-checkbox');
        let isRememberChecked = false;

        rememberCheckbox.addEventListener('click', () => {
            isRememberChecked = !isRememberChecked;
            if (isRememberChecked) {
                rememberCheckbox.classList.add('checked');
            } else {
                rememberCheckbox.classList.remove('checked');
            }
        });

        // 获取验证码功能
        const sendCodeBtn = document.getElementById('send-code-btn');
        let countdown = 0;

        sendCodeBtn.addEventListener('click', () => {
            const phoneInput = document.getElementById('phone');
            const phone = phoneInput.value.trim();
            
            if (!phone) {
                alert('请先输入手机号');
                phoneInput.focus();
                return;
            }

            if (!/^1[3-9]\d{9}$/.test(phone)) {
                alert('请输入正确的手机号');
                phoneInput.focus();
                return;
            }

            // 开始倒计时
            countdown = 60;
            sendCodeBtn.disabled = true;
            
            const timer = setInterval(() => {
                sendCodeBtn.innerHTML = `<span class="countdown">${countdown}s后重新获取</span>`;
                countdown--;
                
                if (countdown < 0) {
                    clearInterval(timer);
                    sendCodeBtn.disabled = false;
                    sendCodeBtn.innerHTML = '获取验证码';
                }
            }, 1000);

            // 模拟发送验证码
            console.log('发送验证码到:', phone);
            alert('验证码已发送，请注意查收');
        });

        // 密码登录表单提交
        document.getElementById('password-form').addEventListener('submit', (e) => {
            e.preventDefault();
            
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value.trim();
            
            if (!username || !password) {
                alert('请填写完整的登录信息');
                return;
            }

            // 模拟登录
            console.log('密码登录:', { username, password, remember: isRememberChecked });
            alert('登录成功！');
        });

        // 短信登录表单提交
        document.getElementById('sms-form').addEventListener('submit', (e) => {
            e.preventDefault();
            
            const phone = document.getElementById('phone').value.trim();
            const code = document.getElementById('verification-code').value.trim();
            
            if (!phone || !code) {
                alert('请填写完整的登录信息');
                return;
            }

            if (!/^1[3-9]\d{9}$/.test(phone)) {
                alert('请输入正确的手机号');
                return;
            }

            if (!/^\d{6}$/.test(code)) {
                alert('请输入6位数字验证码');
                return;
            }

            // 模拟登录
            console.log('短信登录:', { phone, code });
            alert('登录成功！');
        });

        // 忘记密码
        document.querySelector('.forgot-link').addEventListener('click', (e) => {
            e.preventDefault();
            alert('忘记密码功能开发中...');
        });

        // 论坛账号登录
        document.querySelector('.forum-login-btn').addEventListener('click', (e) => {
            e.preventDefault();
            alert('跳转到中国REITs论坛登录页面...');
        });

        // 注册链接
        document.querySelector('.register-link').addEventListener('click', (e) => {
            e.preventDefault();
            alert('跳转到注册页面...');
        });
    </script>
</body>
</html>

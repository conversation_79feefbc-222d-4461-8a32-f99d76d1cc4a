<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>全局仪表盘 - 中国REITs论坛</title>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            margin: 0px;
            background-color: rgba(242, 242, 242, 1);
            position: relative;
            width: 1912px;
            margin-left: auto;
            margin-right: auto;
            text-align: left;
            font-family: "STHeiti SC Medium", "STHeiti SC", sans-serif;
            min-height: 1196px;
            overflow-x: auto;
        }

        /* 顶部导航栏 */
        .top-navbar {
            position: absolute;
            left: 0px;
            top: 0px;
            width: 1912px;
            height: 70px;
            background-color: rgba(255, 255, 255, 1);
            box-shadow: 5px 0px 5px rgba(215, 215, 215, 0.35);
            z-index: 100;
            display: flex;
            align-items: center;
            padding: 0 30px;
        }

        /* Logo区域 */
        .logo-section {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .logo-icon {
            width: 40px;
            height: 40px;
            background-color: #1868F1;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
            font-weight: bold;
        }

        .logo-text {
            font-size: 24px;
            color: #1868F1;
            font-weight: 500;
        }

        /* 用户信息区域 */
        .user-section {
            margin-left: auto;
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .user-avatar {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background-color: #f0f0f0;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
        }

        .user-name {
            font-size: 16px;
            color: #333;
        }

        /* 左侧边栏 */
        .sidebar {
            position: absolute;
            left: 0px;
            top: 70px;
            width: 200px;
            height: 884px;
            background-color: rgba(255, 255, 255, 1);
            border-radius: 0px 10px 10px 0px;
            box-shadow: 5px 0px 5px rgba(215, 215, 215, 0.35);
            z-index: 50;
            padding: 20px 0;
        }

        .sidebar-menu {
            list-style: none;
        }

        .sidebar-menu li {
            padding: 12px 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
        }

        .sidebar-menu li:hover {
            background-color: rgba(24, 104, 241, 0.1);
            border-left-color: #1868F1;
        }

        .sidebar-menu li.active {
            background-color: rgba(24, 104, 241, 0.15);
            border-left-color: #1868F1;
            color: #1868F1;
            font-weight: 500;
        }

        .sidebar-menu li a {
            text-decoration: none;
            color: inherit;
            font-size: 14px;
        }

        /* 主内容区域 */
        .main-content {
            position: absolute;
            left: 220px;
            top: 90px;
            width: calc(1912px - 240px);
            min-height: calc(1196px - 110px);
            padding: 20px;
        }

        /* 页面标题 */
        .page-title {
            font-size: 28px;
            color: #333;
            margin-bottom: 30px;
            font-weight: 500;
        }

        /* 统计卡片容器 */
        .stats-container {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
            transform: translateY(-2px);
        }

        .stat-card-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 16px;
        }

        .stat-card-title {
            font-size: 14px;
            color: #666;
            font-weight: 400;
        }

        .stat-card-icon {
            width: 32px;
            height: 32px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            color: white;
        }

        .stat-card-value {
            font-size: 32px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }

        .stat-card-change {
            font-size: 12px;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .stat-card-change.positive {
            color: #10b981;
        }

        .stat-card-change.negative {
            color: #ef4444;
        }

        /* 图表容器 */
        .charts-container {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }

        .chart-card {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .chart-title {
            font-size: 18px;
            color: #333;
            margin-bottom: 20px;
            font-weight: 500;
        }

        .chart-container {
            width: 100%;
            height: 300px;
        }

        /* 表格容器 */
        .table-container {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .table-title {
            font-size: 18px;
            color: #333;
            margin-bottom: 20px;
            font-weight: 500;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
        }

        .data-table th,
        .data-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #f0f0f0;
        }

        .data-table th {
            background-color: #f8f9fa;
            font-weight: 500;
            color: #666;
            font-size: 14px;
        }

        .data-table td {
            font-size: 14px;
            color: #333;
        }

        .data-table tr:hover {
            background-color: #f8f9fa;
        }

        /* 响应式设计 */
        @media (max-width: 1440px) {
            body {
                width: 100vw;
            }

            .top-navbar {
                width: 100vw;
            }

            .main-content {
                width: calc(100vw - 240px);
            }

            .stats-container {
                grid-template-columns: repeat(2, 1fr);
            }

            .charts-container {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }

            .main-content {
                left: 20px;
                width: calc(100vw - 40px);
            }

            .stats-container {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- 顶部导航栏 -->
    <div class="top-navbar">
        <div class="logo-section">
            <div class="logo-icon">logo</div>
            <div class="logo-text">rev-REITs平台</div>
        </div>
        
        <div class="user-section">
            <div class="user-name">欢迎，用户</div>
            <div class="user-avatar">👤</div>
        </div>
    </div>

    <!-- 左侧边栏 -->
    <div class="sidebar">
        <ul class="sidebar-menu">
            <li class="active"><a href="#dashboard">全局仪表盘</a></li>
            <li><a href="#products">公募REITs产品</a></li>
            <li><a href="#market">市场动态</a></li>
            <li><a href="#industry">行业情况</a></li>
            <li><a href="#reports">文章报告</a></li>
            <li><a href="#profile">个人中心</a></li>
        </ul>
    </div>

    <!-- 主内容区域 -->
    <div class="main-content">
        <h1 class="page-title">全局仪表盘</h1>
        
        <!-- 统计卡片 -->
        <div class="stats-container">
            <div class="stat-card">
                <div class="stat-card-header">
                    <span class="stat-card-title">总市值</span>
                    <div class="stat-card-icon" style="background-color: #3b82f6;">📊</div>
                </div>
                <div class="stat-card-value">¥2,847.5亿</div>
                <div class="stat-card-change positive">
                    ↗ +5.2% 较上月
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-card-header">
                    <span class="stat-card-title">产品数量</span>
                    <div class="stat-card-icon" style="background-color: #10b981;">📈</div>
                </div>
                <div class="stat-card-value">28</div>
                <div class="stat-card-change positive">
                    ↗ +2 新增产品
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-card-header">
                    <span class="stat-card-title">平均收益率</span>
                    <div class="stat-card-icon" style="background-color: #f59e0b;">💰</div>
                </div>
                <div class="stat-card-value">4.8%</div>
                <div class="stat-card-change negative">
                    ↘ -0.3% 较上月
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-card-header">
                    <span class="stat-card-title">交易量</span>
                    <div class="stat-card-icon" style="background-color: #8b5cf6;">📊</div>
                </div>
                <div class="stat-card-value">¥156.8亿</div>
                <div class="stat-card-change positive">
                    ↗ +12.5% 较上月
                </div>
            </div>
        </div>

        <!-- 图表区域 -->
        <div class="charts-container">
            <div class="chart-card">
                <h3 class="chart-title">市值趋势</h3>
                <div id="marketValueChart" class="chart-container"></div>
            </div>
            
            <div class="chart-card">
                <h3 class="chart-title">产品类型分布</h3>
                <div id="productTypeChart" class="chart-container"></div>
            </div>
        </div>

        <!-- 数据表格 -->
        <div class="table-container">
            <h3 class="table-title">热门REITs产品</h3>
            <table class="data-table">
                <thead>
                    <tr>
                        <th>产品名称</th>
                        <th>代码</th>
                        <th>最新价格</th>
                        <th>涨跌幅</th>
                        <th>市值</th>
                        <th>收益率</th>
                    </tr>
                </thead>
                <tbody id="reitsTable">
                    <!-- 数据将通过JavaScript动态填充 -->
                </tbody>
            </table>
        </div>
    </div>
    <script>
        // 初始化页面
        document.addEventListener('DOMContentLoaded', function() {
            initCharts();
            loadTableData();
        });

        // 初始化图表
        function initCharts() {
            // 市值趋势图
            const marketValueChart = echarts.init(document.getElementById('marketValueChart'));
            const marketValueOption = {
                title: {
                    text: '',
                    textStyle: {
                        fontSize: 14,
                        color: '#666'
                    }
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'cross'
                    }
                },
                legend: {
                    data: ['总市值', '交易量'],
                    bottom: 10
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '15%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    boundaryGap: false,
                    data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
                },
                yAxis: [
                    {
                        type: 'value',
                        name: '市值(亿元)',
                        position: 'left',
                        axisLabel: {
                            formatter: '{value}'
                        }
                    },
                    {
                        type: 'value',
                        name: '交易量(亿元)',
                        position: 'right',
                        axisLabel: {
                            formatter: '{value}'
                        }
                    }
                ],
                series: [
                    {
                        name: '总市值',
                        type: 'line',
                        smooth: true,
                        data: [2200, 2350, 2400, 2500, 2650, 2700, 2750, 2800, 2820, 2847, 2860, 2847],
                        itemStyle: {
                            color: '#1868F1'
                        },
                        areaStyle: {
                            color: {
                                type: 'linear',
                                x: 0,
                                y: 0,
                                x2: 0,
                                y2: 1,
                                colorStops: [{
                                    offset: 0, color: 'rgba(24, 104, 241, 0.3)'
                                }, {
                                    offset: 1, color: 'rgba(24, 104, 241, 0.05)'
                                }]
                            }
                        }
                    },
                    {
                        name: '交易量',
                        type: 'bar',
                        yAxisIndex: 1,
                        data: [120, 135, 140, 145, 150, 155, 148, 152, 158, 156, 160, 157],
                        itemStyle: {
                            color: '#10b981'
                        }
                    }
                ]
            };
            marketValueChart.setOption(marketValueOption);

            // 产品类型分布饼图
            const productTypeChart = echarts.init(document.getElementById('productTypeChart'));
            const productTypeOption = {
                title: {
                    text: '',
                    left: 'center'
                },
                tooltip: {
                    trigger: 'item',
                    formatter: '{a} <br/>{b}: {c} ({d}%)'
                },
                legend: {
                    orient: 'vertical',
                    left: 'left',
                    data: ['产业园区', '仓储物流', '保障性租赁住房', '高速公路', '能源基础设施']
                },
                series: [
                    {
                        name: '产品类型',
                        type: 'pie',
                        radius: ['40%', '70%'],
                        avoidLabelOverlap: false,
                        label: {
                            show: false,
                            position: 'center'
                        },
                        emphasis: {
                            label: {
                                show: true,
                                fontSize: '18',
                                fontWeight: 'bold'
                            }
                        },
                        labelLine: {
                            show: false
                        },
                        data: [
                            {value: 8, name: '产业园区', itemStyle: {color: '#1868F1'}},
                            {value: 6, name: '仓储物流', itemStyle: {color: '#10b981'}},
                            {value: 5, name: '保障性租赁住房', itemStyle: {color: '#f59e0b'}},
                            {value: 4, name: '高速公路', itemStyle: {color: '#8b5cf6'}},
                            {value: 5, name: '能源基础设施', itemStyle: {color: '#ef4444'}}
                        ]
                    }
                ]
            };
            productTypeChart.setOption(productTypeOption);

            // 响应式处理
            window.addEventListener('resize', function() {
                marketValueChart.resize();
                productTypeChart.resize();
            });
        }

        // 加载表格数据
        function loadTableData() {
            const tableData = [
                {
                    name: '博时招商产业园REIT',
                    code: '508027',
                    price: '3.456',
                    change: '+2.34%',
                    marketValue: '156.8亿',
                    yield: '4.8%',
                    changeClass: 'positive'
                },
                {
                    name: '红土创新盐田港REIT',
                    code: '180101',
                    price: '2.987',
                    change: '+1.23%',
                    marketValue: '89.5亿',
                    yield: '5.2%',
                    changeClass: 'positive'
                },
                {
                    name: '华安张江光大REIT',
                    code: '508000',
                    price: '2.765',
                    change: '-0.56%',
                    marketValue: '78.3亿',
                    yield: '4.6%',
                    changeClass: 'negative'
                },
                {
                    name: '东吴苏园产业REIT',
                    code: '508001',
                    price: '3.123',
                    change: '+0.89%',
                    marketValue: '92.1亿',
                    yield: '4.9%',
                    changeClass: 'positive'
                },
                {
                    name: '平安广州广河REIT',
                    code: '508006',
                    price: '2.654',
                    change: '*****%',
                    marketValue: '67.8亿',
                    yield: '5.1%',
                    changeClass: 'positive'
                }
            ];

            const tableBody = document.getElementById('reitsTable');
            tableBody.innerHTML = '';

            tableData.forEach(item => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${item.name}</td>
                    <td>${item.code}</td>
                    <td>¥${item.price}</td>
                    <td class="${item.changeClass}">${item.change}</td>
                    <td>${item.marketValue}</td>
                    <td>${item.yield}</td>
                `;
                tableBody.appendChild(row);
            });
        }

        // 侧边栏菜单点击事件
        document.querySelectorAll('.sidebar-menu li').forEach(item => {
            item.addEventListener('click', function() {
                // 移除所有活动状态
                document.querySelectorAll('.sidebar-menu li').forEach(li => li.classList.remove('active'));
                // 添加当前活动状态
                this.classList.add('active');

                // 这里可以添加页面跳转逻辑
                const href = this.querySelector('a').getAttribute('href');
                console.log('导航到:', href);
            });
        });
    </script>

    <style>
        /* 表格中的涨跌幅颜色 */
        .positive {
            color: #10b981;
        }

        .negative {
            color: #ef4444;
        }

        /* 动画效果 */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .stat-card,
        .chart-card,
        .table-container {
            animation: fadeInUp 0.6s ease-out;
        }

        .stat-card:nth-child(1) { animation-delay: 0.1s; }
        .stat-card:nth-child(2) { animation-delay: 0.2s; }
        .stat-card:nth-child(3) { animation-delay: 0.3s; }
        .stat-card:nth-child(4) { animation-delay: 0.4s; }

        .chart-card:nth-child(1) { animation-delay: 0.5s; }
        .chart-card:nth-child(2) { animation-delay: 0.6s; }

        .table-container { animation-delay: 0.7s; }
    </style>
</body>
</html>

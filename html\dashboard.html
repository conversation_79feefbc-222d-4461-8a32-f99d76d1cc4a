<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>全局仪表盘 - 中国REITs论坛</title>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            margin: 0px;
            background-color: #f5f5f5;
            position: relative;
            width: 1912px;
            margin-left: auto;
            margin-right: auto;
            text-align: left;
            font-family: "Microsoft YaHei", "STHeiti SC Medium", "STHeiti SC", sans-serif;
            min-height: 1196px;
            overflow-x: auto;
        }

        /* 顶部导航栏 */
        .top-navbar {
            position: absolute;
            left: 0px;
            top: 0px;
            width: 1912px;
            height: 50px;
            background-color: #4a90e2;
            z-index: 100;
            display: flex;
            align-items: center;
            padding: 0 20px;
        }

        /* Logo区域 */
        .logo-section {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .logo-icon {
            width: 24px;
            height: 24px;
            background-color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #4a90e2;
            font-size: 10px;
            font-weight: bold;
        }

        .logo-text {
            font-size: 16px;
            color: white;
            font-weight: 400;
        }

        /* 用户信息区域 */
        .user-section {
            margin-left: auto;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .user-avatar {
            width: 28px;
            height: 28px;
            border-radius: 50%;
            background-color: rgba(255,255,255,0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
        }

        .user-name {
            font-size: 14px;
            color: white;
        }

        /* 左侧边栏 */
        .sidebar {
            position: absolute;
            left: 0px;
            top: 50px;
            width: 180px;
            height: calc(100vh - 50px);
            background-color: #ffffff;
            border-right: 1px solid #e0e0e0;
            z-index: 50;
            padding: 0;
        }

        .sidebar-header {
            padding: 15px;
            border-bottom: 1px solid #e0e0e0;
            font-size: 14px;
            color: #666;
            font-weight: 500;
        }

        .sidebar-menu {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .sidebar-menu li {
            padding: 0;
            cursor: pointer;
            transition: all 0.3s ease;
            border-bottom: 1px solid #f0f0f0;
        }

        .sidebar-menu li:hover {
            background-color: #f8f9fa;
        }

        .sidebar-menu li.active {
            background-color: #e3f2fd;
            border-left: 3px solid #4a90e2;
        }

        .sidebar-menu li a {
            text-decoration: none;
            color: #333;
            font-size: 13px;
            display: block;
            padding: 12px 15px;
            position: relative;
        }

        .sidebar-menu li.active a {
            color: #4a90e2;
            font-weight: 500;
        }

        /* 主内容区域 */
        .main-content {
            position: absolute;
            left: 180px;
            top: 50px;
            width: calc(1912px - 180px - 350px);
            min-height: calc(100vh - 50px);
            padding: 15px;
            background-color: #f5f5f5;
        }

        /* 右侧面板 */
        .right-panel {
            position: absolute;
            right: 0px;
            top: 50px;
            width: 350px;
            height: calc(100vh - 50px);
            background-color: #ffffff;
            border-left: 1px solid #e0e0e0;
            padding: 15px;
        }

        /* 主要图表区域 */
        .main-chart-area {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 15px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        /* 底部内容区域 - 2x2网格布局 */
        .bottom-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            grid-template-rows: 350px 250px;
            gap: 15px;
        }

        /* 左下角饼图区域 */
        .pie-chart-section {
            background: white;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        /* 中间数据指标区域 */
        .metrics-section {
            background: white;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .metric-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .metric-item:last-child {
            border-bottom: none;
        }

        .metric-label {
            font-size: 14px;
            color: #666;
        }

        .metric-value {
            font-size: 18px;
            font-weight: 600;
            color: #333;
        }

        .metric-change {
            font-size: 12px;
            margin-left: 8px;
        }

        .metric-change.positive {
            color: #4caf50;
        }

        .metric-change.negative {
            color: #f44336;
        }

        /* 右下角条形图区域 */
        .bar-chart-section {
            background: white;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        /* REITs涨幅TOP3模块 */
        .reits-top3-section,
        .reits-bottom3-section {
            background: white;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .top3-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .top3-item:last-child {
            border-bottom: none;
        }

        .top3-rank {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
            color: white;
            margin-right: 12px;
        }

        .top3-rank.rank-1 {
            background: linear-gradient(135deg, #ff6b6b, #ee5a52);
        }

        .top3-rank.rank-2 {
            background: linear-gradient(135deg, #4ecdc4, #44a08d);
        }

        .top3-rank.rank-3 {
            background: linear-gradient(135deg, #45b7d1, #3498db);
        }

        .top3-info {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .top3-name {
            font-size: 14px;
            color: #333;
            font-weight: 500;
            margin-bottom: 2px;
        }

        .top3-code {
            font-size: 12px;
            color: #999;
        }

        .top3-change {
            font-size: 16px;
            font-weight: 600;
            color: #4caf50;
        }

        /* 中国地图区域 */
        .china-map-section {
            background: white;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        /* 资产地域分布模块 */
        .region-distribution-section {
            background: white;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .region-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .region-item:last-child {
            border-bottom: none;
        }

        .region-name {
            font-size: 14px;
            color: #333;
            flex: 1;
        }

        .region-bar {
            width: 100px;
            height: 6px;
            background: #f0f0f0;
            border-radius: 3px;
            margin: 0 10px;
            position: relative;
        }

        .region-bar-fill {
            height: 100%;
            border-radius: 3px;
            transition: width 0.3s ease;
        }

        .region-value {
            font-size: 12px;
            color: #666;
            min-width: 30px;
            text-align: right;
        }

        /* 图表标题 */
        .chart-title {
            font-size: 16px;
            color: #333;
            margin-bottom: 15px;
            font-weight: 500;
        }

        .chart-container {
            width: 100%;
            height: 280px;
        }

        /* 日历样式 */
        .calendar-section {
            margin-bottom: 20px;
        }

        .calendar-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            font-size: 16px;
            font-weight: 500;
        }

        .calendar-grid {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 2px;
            margin-bottom: 15px;
        }

        .calendar-day {
            aspect-ratio: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            cursor: pointer;
            border-radius: 4px;
            transition: background-color 0.2s;
        }

        .calendar-day:hover {
            background-color: #f0f0f0;
        }

        .calendar-day.today {
            background-color: #4a90e2;
            color: white;
        }

        .calendar-day.selected {
            background-color: #e3f2fd;
            color: #4a90e2;
        }

        /* 事件列表 */
        .events-section {
            flex: 1;
        }

        .event-item {
            display: flex;
            align-items: flex-start;
            gap: 10px;
            padding: 10px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .event-item:last-child {
            border-bottom: none;
        }

        .event-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-top: 6px;
            flex-shrink: 0;
        }

        .event-content {
            flex: 1;
        }

        .event-title {
            font-size: 13px;
            color: #333;
            margin-bottom: 2px;
        }

        .event-time {
            font-size: 11px;
            color: #999;
        }

        /* 表格容器 */
        .table-container {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .table-title {
            font-size: 18px;
            color: #333;
            margin-bottom: 20px;
            font-weight: 500;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
        }

        .data-table th,
        .data-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #f0f0f0;
        }

        .data-table th {
            background-color: #f8f9fa;
            font-weight: 500;
            color: #666;
            font-size: 14px;
        }

        .data-table td {
            font-size: 14px;
            color: #333;
        }

        .data-table tr:hover {
            background-color: #f8f9fa;
        }

        /* 响应式设计 */
        @media (max-width: 1440px) {
            body {
                width: 100vw;
            }

            .top-navbar {
                width: 100vw;
            }

            .main-content {
                width: calc(100vw - 240px);
            }

            .stats-container {
                grid-template-columns: repeat(2, 1fr);
            }

            .charts-container {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }

            .main-content {
                left: 20px;
                width: calc(100vw - 40px);
            }

            .stats-container {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- 顶部导航栏 -->
    <div class="top-navbar">
        <div class="logo-section">
            <div class="logo-icon">R</div>
            <div class="logo-text">rev-REITs</div>
        </div>

        <div class="user-section">
            <div class="user-name">用户</div>
            <div class="user-avatar">👤</div>
        </div>
    </div>

    <!-- 左侧边栏 -->
    <div class="sidebar">
        <div class="sidebar-header">导航菜单</div>
        <ul class="sidebar-menu">
            <li class="active"><a href="#dashboard">🏠 全局仪表盘</a></li>
            <li><a href="#market">📈 行业情况展示</a></li>
            <li><a href="#products">📊 公募REITs产品及资产</a></li>
            <li><a href="#industry">🏢 市场动态</a></li>
        </ul>
    </div>

    <!-- 主内容区域 -->
    <div class="main-content">
        <!-- 顶部主要图表区域 -->
        <div class="main-chart-area">
            <h3 class="chart-title">公募REITs市场概览</h3>
            <div id="mainLineChart" class="chart-container"></div>
        </div>

        <!-- 底部2x2网格布局 -->
        <div class="bottom-content">
            <!-- 左上：饼图 -->
            <div class="pie-chart-section">
                <h3 class="chart-title">分行业市值占比(每日更新)</h3>
                <div id="pieChart" class="chart-container" style="height: 280px;"></div>
            </div>

            <!-- 右上：中国地图 -->
            <div class="china-map-section">
                <h3 class="chart-title">资产地域分布</h3>
                <div id="chinaMap" class="chart-container" style="height: 280px;"></div>
            </div>

            <!-- 左下：宏观数据 -->
            <div class="metrics-section">
                <h3 class="chart-title">宏观数据</h3>
                <div class="metric-item">
                    <span class="metric-label">GDP(2024)</span>
                    <div>
                        <span class="metric-value">134.91万亿</span>
                    </div>
                </div>
                <div class="metric-item">
                    <span class="metric-label">CPI</span>
                    <div>
                        <span class="metric-value">0.4%</span>
                        <span class="metric-change positive">↑</span>
                    </div>
                </div>
                <div class="metric-item">
                    <span class="metric-label">平均增长率(2025Q1)</span>
                    <div>
                        <span class="metric-value">5.4%</span>
                        <span class="metric-change positive">↑</span>
                    </div>
                </div>
                <div class="metric-item">
                    <span class="metric-label">LPR</span>
                    <div>
                        <span class="metric-value">3.5%</span>
                        <span class="metric-change positive">↑</span>
                    </div>
                </div>
            </div>

            <!-- 右下：条形图 -->
            <div class="bar-chart-section">
                <h3 class="chart-title">行业平均Cap Rate</h3>
                <div id="barChart" class="chart-container" style="height: 180px;"></div>
            </div>
        </div>
    </div>

    <!-- 右侧面板 -->
    <div class="right-panel">
        <!-- REITs涨幅TOP3(当日) -->
        <div class="reits-top3-section" style="margin-bottom: 15px;">
            <h3 class="chart-title">REITs涨幅TOP3(当日)</h3>
            <div id="reitsTop3List">
                <!-- 数据将通过JavaScript动态填充 -->
            </div>
        </div>

        <!-- REITs跌幅TOP3(当日) -->
        <div class="reits-bottom3-section" style="margin-bottom: 15px;">
            <h3 class="chart-title">REITs跌幅TOP3(当日)</h3>
            <div id="reitsBottom3List">
                <!-- 数据将通过JavaScript动态填充 -->
            </div>
        </div>
    </div>

    <script>
        // 初始化页面
        document.addEventListener('DOMContentLoaded', function() {
            initCharts();
            loadReitsTop3();
            loadReitsBottom3();
        });

        // 初始化图表
        function initCharts() {
            // 主要折线图
            const mainLineChart = echarts.init(document.getElementById('mainLineChart'));
            const mainLineOption = {
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'cross'
                    }
                },
                legend: {
                    data: ['产业园区', '仓储物流', '保障性租赁住房', '高速公路'],
                    top: 10,
                    textStyle: {
                        fontSize: 12
                    }
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    top: '15%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    boundaryGap: false,
                    data: ['2019-01', '2019-04', '2019-07', '2019-10', '2020-01', '2020-04', '2020-07', '2020-10', '2021-01', '2021-04', '2021-07', '2021-10'],
                    axisLabel: {
                        fontSize: 10
                    }
                },
                yAxis: {
                    type: 'value',
                    axisLabel: {
                        fontSize: 10
                    }
                },
                series: [
                    {
                        name: '产业园区',
                        type: 'line',
                        smooth: true,
                        data: [120, 132, 101, 134, 90, 230, 210, 182, 191, 234, 290, 330],
                        itemStyle: { color: '#5470c6' }
                    },
                    {
                        name: '仓储物流',
                        type: 'line',
                        smooth: true,
                        data: [220, 182, 191, 234, 290, 330, 310, 201, 154, 190, 330, 410],
                        itemStyle: { color: '#91cc75' }
                    },
                    {
                        name: '保障性租赁住房',
                        type: 'line',
                        smooth: true,
                        data: [150, 232, 201, 154, 190, 330, 410, 320, 332, 301, 334, 390],
                        itemStyle: { color: '#fac858' }
                    },
                    {
                        name: '高速公路',
                        type: 'line',
                        smooth: true,
                        data: [320, 332, 301, 334, 390, 330, 320, 232, 201, 154, 190, 330],
                        itemStyle: { color: '#ee6666' }
                    }
                ]
            };
            mainLineChart.setOption(mainLineOption);

            // 饼图
            const pieChart = echarts.init(document.getElementById('pieChart'));
            const pieOption = {
                tooltip: {
                    trigger: 'item',
                    formatter: '{b}: {c}% ({d}%)'
                },
                legend: {
                    orient: 'vertical',
                    left: 'right',
                    top: 'center',
                    textStyle: {
                        fontSize: 10
                    },
                    data: ['产业园区', '仓储物流', '保障性租赁住房', '高速公路', '能源基础设施', '其他']
                },
                series: [
                    {
                        type: 'pie',
                        radius: ['30%', '60%'],
                        center: ['40%', '50%'],
                        data: [
                            {value: 28.6, name: '产业园区', itemStyle: {color: '#5470c6'}},
                            {value: 21.4, name: '仓储物流', itemStyle: {color: '#91cc75'}},
                            {value: 17.9, name: '保障性租赁住房', itemStyle: {color: '#fac858'}},
                            {value: 14.3, name: '高速公路', itemStyle: {color: '#ee6666'}},
                            {value: 10.7, name: '能源基础设施', itemStyle: {color: '#73c0de'}},
                            {value: 7.1, name: '其他', itemStyle: {color: '#3ba272'}}
                        ],
                        label: {
                            show: false
                        },
                        labelLine: {
                            show: false
                        }
                    }
                ]
            };
            pieChart.setOption(pieOption);

            // 中国地图
            const chinaMap = echarts.init(document.getElementById('chinaMap'));
            const mapOption = {
                tooltip: {
                    trigger: 'item',
                    formatter: '{b}: {c}个项目'
                },
                visualMap: {
                    min: 0,
                    max: 10,
                    left: 'left',
                    top: 'bottom',
                    text: ['高', '低'],
                    calculable: true,
                    inRange: {
                        color: ['#e0f3ff', '#006edd']
                    },
                    textStyle: {
                        fontSize: 10
                    }
                },
                series: [
                    {
                        type: 'map',
                        map: 'china',
                        roam: false,
                        label: {
                            show: false
                        },
                        data: [
                            {name: '北京', value: 8},
                            {name: '上海', value: 6},
                            {name: '广东', value: 5},
                            {name: '江苏', value: 4},
                            {name: '浙江', value: 3},
                            {name: '山东', value: 2},
                            {name: '河南', value: 1}
                        ]
                    }
                ]
            };
            // 注意：实际使用时需要引入中国地图数据
            // chinaMap.setOption(mapOption);

            // 条形图
            const barChart = echarts.init(document.getElementById('barChart'));
            const barOption = {
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    }
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    top: '3%',
                    containLabel: true
                },
                xAxis: {
                    type: 'value',
                    axisLabel: {
                        fontSize: 10
                    }
                },
                yAxis: {
                    type: 'category',
                    data: ['能源基础设施', '高速公路', '保障性租赁住房', '仓储物流', '产业园区'],
                    axisLabel: {
                        fontSize: 10
                    }
                },
                series: [
                    {
                        type: 'bar',
                        data: [
                            {value: 6.2, itemStyle: {color: '#73c0de'}},
                            {value: 5.8, itemStyle: {color: '#ee6666'}},
                            {value: 5.4, itemStyle: {color: '#fac858'}},
                            {value: 4.9, itemStyle: {color: '#91cc75'}},
                            {value: 4.5, itemStyle: {color: '#5470c6'}}
                        ],
                        barWidth: '60%'
                    }
                ]
            };
            barChart.setOption(barOption);

            // 响应式处理
            window.addEventListener('resize', function() {
                mainLineChart.resize();
                pieChart.resize();
                chinaMap.resize();
                barChart.resize();
            });
        }

        // 初始化日历
        function initCalendar() {
            const calendarGrid = document.getElementById('calendarGrid');
            const today = new Date();
            const year = today.getFullYear();
            const month = today.getMonth();

            // 添加星期标题
            const weekdays = ['日', '一', '二', '三', '四', '五', '六'];
            weekdays.forEach(day => {
                const dayElement = document.createElement('div');
                dayElement.className = 'calendar-day';
                dayElement.style.color = '#999';
                dayElement.style.fontWeight = 'bold';
                dayElement.textContent = day;
                calendarGrid.appendChild(dayElement);
            });

            // 获取当月第一天是星期几
            const firstDay = new Date(year, month, 1).getDay();
            // 获取当月天数
            const daysInMonth = new Date(year, month + 1, 0).getDate();

            // 添加空白天数
            for (let i = 0; i < firstDay; i++) {
                const emptyDay = document.createElement('div');
                emptyDay.className = 'calendar-day';
                calendarGrid.appendChild(emptyDay);
            }

            // 添加日期
            for (let day = 1; day <= daysInMonth; day++) {
                const dayElement = document.createElement('div');
                dayElement.className = 'calendar-day';
                dayElement.textContent = day;

                // 标记今天
                if (day === today.getDate()) {
                    dayElement.classList.add('today');
                }

                // 标记特殊日期
                if ([9, 17, 23, 25].includes(day)) {
                    dayElement.classList.add('selected');
                }

                calendarGrid.appendChild(dayElement);
            }
        }

        // 日历导航函数
        function prevMonth() {
            console.log('上一月');
        }

        function nextMonth() {
            console.log('下一月');
        }

        // 加载REITs涨幅TOP3数据
        function loadReitsTop3() {
            const top3Data = [
                {
                    rank: 1,
                    name: '嘉实京东仓储基础设施封闭式基础设施证券投资基金',
                    code: '508027',
                    change: '+3.42%'
                },
                {
                    rank: 2,
                    name: '易方天华中核高速公路封闭式基础设施证券投资基金',
                    code: '180101',
                    change: '*****%'
                },
                {
                    rank: 3,
                    name: '中信建投明德智能制造封闭式基础设施证券投资基金',
                    code: '508000',
                    change: '*****%'
                }
            ];

            const container = document.getElementById('reitsTop3List');
            container.innerHTML = '';

            top3Data.forEach(item => {
                const itemElement = document.createElement('div');
                itemElement.className = 'top3-item';
                itemElement.innerHTML = `
                    <div style="display: flex; align-items: center;">
                        <div class="top3-rank rank-${item.rank}">${item.rank}</div>
                        <div class="top3-info">
                            <div class="top3-name">${item.name}</div>
                            <div class="top3-code">${item.code}</div>
                        </div>
                    </div>
                    <div class="top3-change" style="color: #4caf50;">${item.change}</div>
                `;
                container.appendChild(itemElement);
            });
        }

        // 加载REITs跌幅TOP3数据
        function loadReitsBottom3() {
            const bottom3Data = [
                {
                    rank: 1,
                    name: '嘉实京东仓储基础设施封闭式基础设施证券投资基金',
                    code: '508027',
                    change: '↓3.42%'
                },
                {
                    rank: 2,
                    name: '易方天华中核高速公路封闭式基础设施证券投资基金',
                    code: '180101',
                    change: '↓2.77%'
                },
                {
                    rank: 3,
                    name: '中信建投明德智能制造封闭式基础设施证券投资基金',
                    code: '508000',
                    change: '↓1.88%'
                }
            ];

            const container = document.getElementById('reitsBottom3List');
            container.innerHTML = '';

            bottom3Data.forEach(item => {
                const itemElement = document.createElement('div');
                itemElement.className = 'top3-item';
                itemElement.innerHTML = `
                    <div style="display: flex; align-items: center;">
                        <div class="top3-rank rank-${item.rank}">${item.rank}</div>
                        <div class="top3-info">
                            <div class="top3-name">${item.name}</div>
                            <div class="top3-code">${item.code}</div>
                        </div>
                    </div>
                    <div class="top3-change" style="color: #f44336;">${item.change}</div>
                `;
                container.appendChild(itemElement);
            });
        }



        // 侧边栏菜单点击事件
        document.querySelectorAll('.sidebar-menu li').forEach(item => {
            item.addEventListener('click', function() {
                // 移除所有活动状态
                document.querySelectorAll('.sidebar-menu li').forEach(li => li.classList.remove('active'));
                // 添加当前活动状态
                this.classList.add('active');

                // 这里可以添加页面跳转逻辑
                const href = this.querySelector('a').getAttribute('href');
                console.log('导航到:', href);
            });
        });
    </script>

    <style>
        /* 表格中的涨跌幅颜色 */
        .positive {
            color: #10b981;
        }

        .negative {
            color: #ef4444;
        }

        /* 动画效果 */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .stat-card,
        .chart-card,
        .table-container {
            animation: fadeInUp 0.6s ease-out;
        }

        .stat-card:nth-child(1) { animation-delay: 0.1s; }
        .stat-card:nth-child(2) { animation-delay: 0.2s; }
        .stat-card:nth-child(3) { animation-delay: 0.3s; }
        .stat-card:nth-child(4) { animation-delay: 0.4s; }

        .chart-card:nth-child(1) { animation-delay: 0.5s; }
        .chart-card:nth-child(2) { animation-delay: 0.6s; }

        .table-container { animation-delay: 0.7s; }
    </style>
</body>
</html>
